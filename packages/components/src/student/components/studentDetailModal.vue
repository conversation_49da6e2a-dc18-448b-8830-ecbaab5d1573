<script setup lang="ts">
  import { computed, ref } from 'vue';
  import { IconUser, IconHome, IconFile, IconDown, IconRight } from '@arco-design/web-vue/es/icon';
  import { marked } from 'marked';
  import AttachmentPreviewModal from '@repo/ui/components/data-display/attachmentPreviewModal.vue';
  import { VuePrintNext } from 'vue-print-next';
  import { options } from '../formConfig';
  // import student from '../../../schemas/student';

  const props = defineProps({
    visible: {
      type: Boolean,
      default: false,
    },
    student: {
      type: Object,
      required: true,
    },
  });

  const emits = defineEmits(['update:visible']);

  const visible = computed({
    get: () => props.visible,
    set: (val) => emits('update:visible', val),
  });

  // 控制各个板块的展开状态
  const expandedSections = ref({
    basic: true,
    rehabilitation: false,
    health: false,
    family: false,
    environment: false,
    reinforcers: false,
    development: false,
    attachments: false,
  });

  // 切换板块展开状态
  const toggleSection = (section: string) => {
    expandedSections.value[section] = !expandedSections.value[section];
  };

  // 格式化日期显示
  const formatDate = (date: string) => {
    if (!date) return '';
    return new Date(date).toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
    });
  };

  // 计算年龄
  const calculateAge = (birthday: string) => {
    if (!birthday) return '';
    const birth = new Date(birthday);
    const today = new Date();
    let age = today.getFullYear() - birth.getFullYear();
    const monthDiff = today.getMonth() - birth.getMonth();
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
      age -= 1;
    }
    return age;
  };

  // 检查板块是否有数据
  const hasBasicInfo = computed(() => {
    const s = props.student;
    return (
      s?.name ||
      s?.gender ||
      s?.nation ||
      s?.birthday ||
      s?.fusionSchool?.name ||
      s?.gradeClass?.name ||
      s?.enrollmentYear ||
      s?.disabilityCertificateNo ||
      s?.idCardNo ||
      s?.symbol
    );
  });

  const hasRehabilitationInfo = computed(() => {
    const s = props.student;
    return s?.disorders || s?.disabilityLevel;
  });

  const hasFamilyInfo = computed(() => {
    const s = props.student;
    return s?.guardian || s?.guardianPhone || s?.address || s?.familyMembers?.length;
  });

  const hasDetailedInfo = computed(() => {
    const s = props.student;
    const baseInfo = s?.additionalData?.baseInfo;
    return (
      s?.familyStatus ||
      s?.historyOfDevelopment ||
      s?.historyOfEducation ||
      baseInfo?.growthHistory ||
      baseInfo?.rehabilitationHistory ||
      baseInfo?.futureExpectations ||
      baseInfo?.recentGoals
    );
  });

  const hasHealthMedicalInfo = computed(() => {
    const baseInfo = props.student?.additionalData?.baseInfo;
    return (
      baseInfo?.hasMajorIllness ||
      baseInfo?.accompanyingSymptoms ||
      baseInfo?.hasRegularVisits ||
      baseInfo?.hasLongTermMedication ||
      baseInfo?.hasAllergies ||
      baseInfo?.medicalAdvice
    );
  });

  const hasFamilyEnvironmentInfo = computed(() => {
    const baseInfo = props.student?.additionalData?.baseInfo;
    return (
      props.student?.additionalData?.onlyChild !== undefined ||
      baseInfo?.hasIndependentSpace !== undefined ||
      baseInfo?.afterSchoolSchedule ||
      baseInfo?.holidaySchedule ||
      baseInfo?.parentMaritalStatus
    );
  });

  const hasReinforcersInfo = computed(() => {
    const baseInfo = props.student?.additionalData?.baseInfo;
    return (
      baseInfo?.subjectPreference?.length ||
      baseInfo?.readingPreference?.length ||
      baseInfo?.tokenForm?.length ||
      baseInfo?.foodPreference?.length ||
      baseInfo?.toyPreference?.length ||
      baseInfo?.activityPreference?.length ||
      baseInfo?.aversiveStimuli
    );
  });

  const hasAttachments = computed(() => {
    return props.student?.attachments?.length > 0;
  });

  // 格式化数组显示
  const formatArray = (arr: string[] | undefined) => {
    if (!arr || !Array.isArray(arr) || arr.length === 0) return '无';
    return arr.filter((item) => item && item !== '无').join('、') || '无';
  };

  // 获取父母信息
  const getFatherInfo = computed(() => {
    return props.student?.additionalData?.baseInfo?.fatherInfo || {};
  });

  const getMotherInfo = computed(() => {
    return props.student?.additionalData?.baseInfo?.motherInfo || {};
  });

  const getTypeName = (type: string) => {
    return options.studentAttachmentTypeOptions.find((t) => t.value === type)?.label || '附件';
  };
  const currentAttachment = ref([]);
  const view = ref(false);
  const handleViewAttachment = (item: any) => {
    currentAttachment.value = item;
    view.value = true;
  };

  const getDisableInfo = (disorders: string) => {
    switch (disorders) {
      case '视力残疾':
        return { label: '视力障碍类型', value: props.student?.additionalData?.baseInfo?.vision };
      case '智力残疾':
        return { label: 'IQ值', value: props.student?.additionalData?.baseInfo?.IQ };
      case '多重残疾':
        return { label: '多重障碍说明', value: props.student?.additionalData?.baseInfo?.multiDisabilitiesDescription };
      default:
        return {};
    }
  };

  const handlePrint = () => {
    // eslint-disable-next-line no-new
    new VuePrintNext({
      el: `#studentDetail`,
      popTitle: props.student.name,
      zIndex: 9999,
      printMode: 'popup',
      hide: '.no-print',
    });
  };
</script>

<template>
  <a-modal v-model:visible="visible" width="900px" :footer="false" class="student-detail-modal" :render-to-body="false">
    <template #title>
      <div class="flex justify-between items-center w-full">
        <span class="flex-grow text-center">{{ student?.name }}</span>
        <a-button size="mini" class="flex-shrink-0 text-gray-500 cursor-pointer mr-10" @click="handlePrint">
          <icon-printer />
          打印
        </a-button>
      </div>
    </template>
    <div id="studentDetail" class="max-h-[70vh] overflow-y-auto">
      <!-- 学生基本信息头部 -->
      <div
        class="flex items-center gap-4 p-4 rounded mb-6 bg-gradient-to-r from-gray-50"
        :class="student?.gender === '男' ? 'to-blue-50' : 'to-red-50'"
      >
        <div class="w-16 h-20 bg-gray-100 rounded flex items-center justify-center text-gray-500 text-xs">
          <img :src="student?.additionalData?.avatar?.url" class="w-full h-full object-cover" />
        </div>

        <div class="flex-1">
          <h3 class="text-xl font-semibold text-gray-900 mb-2">{{ student?.name }}</h3>
          <div class="flex flex-wrap gap-4 text-sm text-gray-600">
            <span v-if="student?.gender">性别：{{ student.gender }}</span>
            <span v-if="student?.birthday">
              出生日期：{{ formatDate(student.birthday) }}
              <span v-if="calculateAge(student.birthday)" class="ml-1">({{ calculateAge(student.birthday) }}岁)</span>
            </span>
            <span v-if="student?.fusionSchool?.name">学校：{{ student.fusionSchool.name }}</span>
            <span v-if="student?.symbol">学籍号：{{ student.symbol }}</span>
          </div>
        </div>
      </div>

      <!-- 基本信息板块 -->
      <div v-if="hasBasicInfo" class="mb-4">
        <div
          class="flex items-center justify-between p-3 border rounded cursor-pointer hover:bg-gray-50"
          @click.stop="toggleSection('basic')"
        >
          <div class="flex items-center gap-3">
            <div class="w-8 h-8 bg-blue-500 rounded flex items-center justify-center">
              <IconUser class="text-white text-sm" />
            </div>
            <span class="font-medium text-gray-900">学生基础信息</span>
          </div>
          <IconDown v-if="expandedSections.basic" class="text-gray-400" />
          <IconRight v-else class="text-gray-400" />
        </div>

        <div v-if="expandedSections.basic" class="mt-2 p-4 bg-gray-50 rounded">
          <div class="grid grid-cols-2 gap-4">
            <div v-if="student?.name" class="flex">
              <span class="w-20 text-gray-600">姓名：</span>
              <span class="text-gray-900">{{ student.name }}</span>
            </div>
            <div v-if="student?.gender" class="flex">
              <span class="w-20 text-gray-600">性别：</span>
              <span class="text-gray-900">{{ student.gender }}</span>
            </div>
            <div v-if="student?.nation" class="flex">
              <span class="w-20 text-gray-600">民族：</span>
              <span class="text-gray-900">{{ student.nation }}</span>
            </div>
            <div v-if="student?.birthday" class="flex">
              <span class="w-20 text-gray-600">出生日期：</span>
              <span class="text-gray-900">{{ formatDate(student.birthday) }}</span>
            </div>
            <div v-if="student?.idCardNo" class="flex">
              <span class="w-20 text-gray-600">身份证号：</span>
              <span class="text-gray-900">{{ student.idCardNo }}</span>
            </div>
            <div v-if="student?.fusionSchool?.name" class="flex">
              <span class="w-20 text-gray-600">所属学校：</span>
              <span class="text-gray-900">{{ student.fusionSchool.name }}</span>
            </div>
            <div v-if="student?.gradeClass?.name" class="flex">
              <span class="w-20 text-gray-600">年级班级：</span>
              <span class="text-gray-900">{{ student.gradeClass.name }}</span>
            </div>
            <div v-if="student?.enrollmentYear" class="flex">
              <span class="w-20 text-gray-600">入学年份：</span>
              <span class="text-gray-900">{{ student.enrollmentYear }}</span>
            </div>
            <div v-if="student?.disabilityCertificateNo" class="flex">
              <span class="w-20 text-gray-600">残疾证号：</span>
              <span class="text-gray-900">{{ student.disabilityCertificateNo }}</span>
            </div>
            <div v-if="student?.hasSendPlan !== undefined" class="flex">
              <span class="w-20 text-gray-600">送教学生：</span>
              <span class="text-gray-900">{{ student.hasSendPlan ? '是' : '否' }}</span>
            </div>
            <div v-if="student?.homeDeliveryInstitution?.name" class="flex">
              <span class="w-20 text-gray-600">送教机构：</span>
              <span class="text-gray-900">{{ student.homeDeliveryInstitution.name }}</span>
            </div>
            <div v-if="student?.rehabilitationInstitutionList?.length" class="flex-1">
              <span class="w-20 text-gray-600">康复机构：</span>
              <span class="text-gray-900">{{
                student.rehabilitationInstitutionList.map((item) => item.name).join('、')
              }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 康复信息板块 -->
      <div v-if="hasRehabilitationInfo" class="mb-4">
        <div
          class="flex items-center justify-between p-3 border rounded cursor-pointer hover:bg-gray-50"
          @click.stop="toggleSection('rehabilitation')"
        >
          <div class="flex items-center gap-3">
            <div class="w-8 h-8 bg-red-500 rounded flex items-center justify-center">
              <IconUser class="text-white text-sm" />
            </div>
            <span class="font-medium text-gray-900">残疾与鉴定信息</span>
          </div>
          <IconDown v-if="expandedSections.rehabilitation" class="text-gray-400" />
          <IconRight v-else class="text-gray-400" />
        </div>

        <div v-if="expandedSections.rehabilitation" class="mt-2 p-4 bg-gray-50 rounded">
          <div class="space-y-3">
            <div v-if="student?.disorders" class="flex flex-col text-gray-600">
              <div>
                <span class="w-20">障碍类型：</span>
                <span class="text-gray-900">
                  {{ Array.isArray(student.disorders) ? student.disorders.join('、') : student.disorders }}
                </span>
              </div>
              <div>
                <span>{{ getDisableInfo(student.disorders)?.label }}</span>
                <span>{{ getDisableInfo(student.disorders)?.value }}</span>
              </div>
            </div>
            <div v-if="student?.disabilityLevel" class="flex">
              <span class="w-20 text-gray-600">残疾程度：</span>
              <span class="text-gray-900">{{ student.disabilityLevel }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 健康与医疗信息板块 -->
      <div v-if="hasHealthMedicalInfo" class="mb-4">
        <div
          class="flex items-center justify-between p-3 border rounded cursor-pointer hover:bg-gray-50"
          @click.stop="toggleSection('health')"
        >
          <div class="flex items-center gap-3">
            <div class="w-8 h-8 bg-red-500 rounded flex items-center justify-center">
              <IconUser class="text-white text-sm" />
            </div>
            <span class="font-medium text-gray-900">健康与医疗信息</span>
          </div>
          <IconDown v-if="expandedSections.health" class="text-gray-400" />
          <IconRight v-else class="text-gray-400" />
        </div>

        <div v-if="expandedSections.health" class="mt-2 p-4 bg-gray-50 rounded">
          <div class="bg-white p-4 rounded">
            <h5 class="font-medium text-gray-900 mb-3">身体检查情况</h5>
            <p
              class="text-gray-700 text-sm leading-relaxed mb-4"
              v-html="marked(student?.additionalData?.baseInfo?.physicalExamStatus)"
            />

            <div class="grid grid-cols-2 gap-4 text-sm">
              <div class="flex">
                <span class="w-24 text-gray-600">个人重大疾病/意外：</span>
                <span class="text-gray-900">{{ student?.additionalData?.baseInfo?.hasMajorIllness || '无' }}</span>
              </div>
              <div class="flex">
                <span class="w-24 text-gray-600">伴随症状：</span>
                <span class="text-gray-900">{{ student?.additionalData?.baseInfo?.accompanyingSymptoms || '无' }}</span>
              </div>
              <div class="flex">
                <span class="w-24 text-gray-600">定期看诊：</span>
                <span class="text-gray-900">{{ student?.additionalData?.baseInfo?.hasRegularVisits || '无' }}</span>
              </div>
              <div class="flex">
                <span class="w-24 text-gray-600">长期用药：</span>
                <span class="text-gray-900">{{
                  student?.additionalData?.baseInfo?.hasLongTermMedication || '无'
                }}</span>
              </div>
              <div class="flex">
                <span class="w-24 text-gray-600">过敏情况：</span>
                <span class="text-gray-900">{{ student?.additionalData?.baseInfo?.hasAllergies || '无' }}</span>
              </div>
              <div class="flex">
                <span class="w-24 text-gray-600">医嘱：</span>
                <span class="text-gray-900">{{ student?.additionalData?.baseInfo?.medicalAdvice || '无' }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 家庭状况板块 -->
      <div v-if="hasFamilyInfo" class="mb-4">
        <div
          class="flex items-center justify-between p-3 border rounded cursor-pointer hover:bg-gray-50"
          @click.stop="toggleSection('family')"
        >
          <div class="flex items-center gap-3">
            <div class="w-8 h-8 bg-green-500 rounded flex items-center justify-center">
              <IconHome class="text-white text-sm" />
            </div>
            <span class="font-medium text-gray-900">家庭成员信息</span>
          </div>
          <IconDown v-if="expandedSections.family" class="text-gray-400" />
          <IconRight v-else class="text-gray-400" />
        </div>

        <div v-if="expandedSections.family" class="mt-2 p-4 bg-gray-50 rounded">
          <!-- 监护人信息 -->
          <div v-if="student?.guardian || student?.guardianPhone" class="mb-4 p-3 bg-white rounded">
            <h4 class="font-medium text-gray-900 mb-3 flex items-center">
              <span class="w-6 h-6 bg-amber-500 rounded-full flex items-center justify-center text-white text-xs mr-2"
                >👤</span
              >
              监护人信息
            </h4>
            <div class="grid grid-cols-2 gap-3">
              <div v-if="student?.guardian" class="flex">
                <span class="w-16 text-gray-600">姓名：</span>
                <span class="text-gray-900">{{ student.guardian }}</span>
              </div>
              <div v-if="student?.guardianPhone" class="flex">
                <span class="w-16 text-gray-600">联系电话：</span>
                <span class="text-gray-900">{{ student.guardianPhone }}</span>
              </div>
            </div>
          </div>

          <!-- 家庭成员 -->
          <div v-if="getFatherInfo.education || getMotherInfo.education || student?.familyMembers?.length" class="mb-4">
            <h4 class="font-medium text-gray-900 mb-3">家庭成员</h4>
            <div class="space-y-3">
              <!-- 父亲信息 -->
              <div
                v-if="getFatherInfo.education || getFatherInfo.occupation || getFatherInfo.age || getFatherInfo.contact"
                class="p-3 bg-blue-50 border border-blue-200 rounded"
              >
                <h5 class="font-medium text-blue-900 mb-2 flex items-center">
                  <span
                    class="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center text-white text-xs mr-2"
                    >👨</span
                  >
                  父亲信息
                </h5>
                <div class="grid grid-cols-2 gap-3 text-sm">
                  <div v-if="getFatherInfo.education" class="flex">
                    <span class="w-16 text-gray-600">教育程度：</span>
                    <span class="text-gray-900">{{ getFatherInfo.education }}</span>
                  </div>
                  <div v-if="getFatherInfo.occupation" class="flex">
                    <span class="w-16 text-gray-600">职业：</span>
                    <span class="text-gray-900">{{ getFatherInfo.occupation }}</span>
                  </div>
                  <div v-if="getFatherInfo.age" class="flex">
                    <span class="w-16 text-gray-600">年龄：</span>
                    <span class="text-gray-900">{{ getFatherInfo.age }}</span>
                  </div>
                  <div v-if="getFatherInfo.contact" class="flex">
                    <span class="w-16 text-gray-600">联系电话：</span>
                    <span class="text-gray-900">{{ getFatherInfo.contact }}</span>
                  </div>
                </div>
              </div>

              <!-- 母亲信息 -->
              <div
                v-if="getMotherInfo.education || getMotherInfo.occupation || getMotherInfo.age || getMotherInfo.contact"
                class="p-3 bg-pink-50 border border-pink-200 rounded"
              >
                <h5 class="font-medium text-pink-900 mb-2 flex items-center">
                  <span
                    class="w-6 h-6 bg-pink-500 rounded-full flex items-center justify-center text-white text-xs mr-2"
                    >👩</span
                  >
                  母亲信息
                </h5>
                <div class="grid grid-cols-2 gap-3 text-sm">
                  <div v-if="getMotherInfo.education" class="flex">
                    <span class="w-16 text-gray-600">教育程度：</span>
                    <span class="text-gray-900">{{ getMotherInfo.education }}</span>
                  </div>
                  <div v-if="getMotherInfo.occupation" class="flex">
                    <span class="w-16 text-gray-600">职业：</span>
                    <span class="text-gray-900">{{ getMotherInfo.occupation }}</span>
                  </div>
                  <div v-if="getMotherInfo.age" class="flex">
                    <span class="w-16 text-gray-600">年龄：</span>
                    <span class="text-gray-900">{{ getMotherInfo.age }}</span>
                  </div>
                  <div v-if="getMotherInfo.contact" class="flex">
                    <span class="w-16 text-gray-600">联系电话：</span>
                    <span class="text-gray-900">{{ getMotherInfo.contact }}</span>
                  </div>
                </div>
              </div>

              <!-- 其他家庭成员 -->
              <div v-if="student?.familyMembers?.length" class="space-y-2">
                <div
                  v-for="(member, index) in student.familyMembers"
                  :key="index"
                  class="p-3 bg-gray-50 border border-gray-200 rounded"
                >
                  <div class="grid grid-cols-2 gap-3 text-sm">
                    <div v-if="member.relationship" class="flex">
                      <span class="w-16 text-gray-600">关系：</span>
                      <span class="text-gray-900">{{ member.relationship }}</span>
                    </div>
                    <div v-if="member.name" class="flex">
                      <span class="w-16 text-gray-600">姓名：</span>
                      <span class="text-gray-900">{{ member.name }}</span>
                    </div>
                    <div v-if="member.age" class="flex">
                      <span class="w-16 text-gray-600">年龄：</span>
                      <span class="text-gray-900">{{ member.age }}</span>
                    </div>
                    <div v-if="member.occupation" class="flex">
                      <span class="w-16 text-gray-600">职业：</span>
                      <span class="text-gray-900">{{ member.occupation }}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div v-if="student?.address" class="flex">
            <span class="w-20 text-gray-600">联系地址：</span>
            <span class="text-gray-900">{{ student.address }}</span>
          </div>
        </div>
      </div>

      <!-- 家庭环境与发展信息板块 -->
      <div v-if="hasFamilyEnvironmentInfo" class="mb-4">
        <div
          class="flex items-center justify-between p-3 border rounded cursor-pointer hover:bg-gray-50"
          @click.stop="toggleSection('environment')"
        >
          <div class="flex items-center gap-3">
            <div class="w-8 h-8 bg-orange-500 rounded flex items-center justify-center">
              <IconHome class="text-white text-sm" />
            </div>
            <span class="font-medium text-gray-900">家庭环境与发展信息</span>
          </div>
          <IconDown v-if="expandedSections.environment" class="text-gray-400" />
          <IconRight v-else class="text-gray-400" />
        </div>

        <div v-if="expandedSections.environment" class="mt-2 p-4 bg-gray-50 rounded">
          <div class="space-y-4">
            <!-- 基本家庭信息 -->
            <div class="grid grid-cols-2 gap-4">
              <div v-if="student?.additionalData?.onlyChild !== undefined" class="flex">
                <span class="w-24 text-gray-600">是否独生子女：</span>
                <span class="text-gray-900">{{ student?.additionalData.onlyChild }}</span>
              </div>
              <div v-if="student?.additionalData?.baseInfo?.hasIndependentSpace !== undefined" class="flex">
                <span class="w-24 text-gray-600">是否有独立生活/学习空间：</span>
                <span class="text-gray-900">{{ student?.additionalData.baseInfo.hasIndependentSpace }}</span>
              </div>
            </div>

            <!-- 时间安排 -->
            <div
              v-if="student?.additionalData?.baseInfo?.afterSchoolSchedule"
              class="bg-indigo-50 border border-indigo-200 rounded p-4"
            >
              <h5 class="font-medium text-indigo-900 mb-3 flex items-center">
                <span
                  class="w-6 h-6 bg-indigo-500 rounded-full flex items-center justify-center text-white text-xs mr-2"
                  >📅</span
                >
                放学后时间安排
              </h5>
              <div class="grid grid-cols-2 md:grid-cols-4 gap-3 text-sm">
                <div class="flex">
                  <span class="w-16 text-gray-600">做功课：</span>
                  <span class="text-gray-900"
                    >{{ student?.additionalData.baseInfo.afterSchoolSchedule.homework || 0 }}小时</span
                  >
                </div>
                <div class="flex">
                  <span class="w-16 text-gray-600">帮忙家务：</span>
                  <span class="text-gray-900"
                    >{{ student?.additionalData.baseInfo.afterSchoolSchedule.chores || 0 }}小时</span
                  >
                </div>
                <div class="flex">
                  <span class="w-16 text-gray-600">休闲：</span>
                  <span class="text-gray-900"
                    >{{ student?.additionalData.baseInfo.afterSchoolSchedule.leisure || 0 }}小时</span
                  >
                </div>
                <div class="flex">
                  <span class="w-16 text-gray-600">其他：</span>
                  <span class="text-gray-900"
                    >{{ student?.additionalData.baseInfo.afterSchoolSchedule.other || 0 }}小时</span
                  >
                </div>
              </div>
              <div v-if="student?.additionalData.baseInfo.afterSchoolSchedule.description" class="mt-3">
                <span class="text-gray-600">其他活动说明：</span>
                <span class="text-gray-900">{{
                  student?.additionalData.baseInfo.afterSchoolSchedule.description
                }}</span>
              </div>
            </div>

            <div
              v-if="student?.additionalData?.baseInfo?.holidaySchedule"
              class="bg-emerald-50 border border-emerald-200 rounded p-4"
            >
              <h5 class="font-medium text-emerald-900 mb-3 flex items-center">
                <span
                  class="w-6 h-6 bg-emerald-500 rounded-full flex items-center justify-center text-white text-xs mr-2"
                  >🏖️</span
                >
                假日时间安排
              </h5>
              <div class="grid grid-cols-2 md:grid-cols-4 gap-3 text-sm">
                <div class="flex">
                  <span class="w-16 text-gray-600">做功课：</span>
                  <span class="text-gray-900"
                    >{{ student?.additionalData.baseInfo.holidaySchedule.homework || 0 }}小时</span
                  >
                </div>
                <div class="flex">
                  <span class="w-16 text-gray-600">帮忙家务：</span>
                  <span class="text-gray-900"
                    >{{ student?.additionalData.baseInfo.holidaySchedule.chores || 0 }}小时</span
                  >
                </div>
                <div class="flex">
                  <span class="w-16 text-gray-600">休闲：</span>
                  <span class="text-gray-900"
                    >{{ student?.additionalData.baseInfo.holidaySchedule.leisure || 0 }}小时</span
                  >
                </div>
                <div class="flex">
                  <span class="w-16 text-gray-600">其他：</span>
                  <span class="text-gray-900"
                    >{{ student?.additionalData.baseInfo.holidaySchedule.other || 0 }}小时</span
                  >
                </div>
              </div>
              <div v-if="student?.additionalData.baseInfo.holidaySchedule.description" class="mt-3">
                <span class="text-gray-600">其他活动说明：</span>
                <span class="text-gray-900">{{ student?.additionalData.baseInfo.holidaySchedule.description }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 强化物与偏好信息板块 -->
      <div v-if="hasReinforcersInfo" class="mb-4">
        <div
          class="flex items-center justify-between p-3 border rounded cursor-pointer hover:bg-gray-50"
          @click.stop="toggleSection('reinforcers')"
        >
          <div class="flex items-center gap-3">
            <div class="w-8 h-8 bg-pink-500 rounded flex items-center justify-center">
              <span class="text-white text-sm">⭐</span>
            </div>
            <span class="font-medium text-gray-900">强化物与偏好信息</span>
          </div>
          <IconDown v-if="expandedSections.reinforcers" class="text-gray-400" />
          <IconRight v-else class="text-gray-400" />
        </div>

        <div v-if="expandedSections.reinforcers" class="mt-2 p-4 bg-gray-50 rounded">
          <div class="grid grid-cols-1 lg:grid-cols-2 gap-4">
            <div v-if="student?.additionalData?.baseInfo?.subjectPreference?.length" class="bg-white p-3 rounded">
              <h5 class="font-medium text-gray-900 mb-2">学科偏好</h5>
              <p class="text-gray-700 text-sm">{{ formatArray(student?.additionalData.baseInfo.subjectPreference) }}</p>
            </div>
            <div v-if="student?.additionalData?.baseInfo?.readingPreference?.length" class="bg-white p-3 rounded">
              <h5 class="font-medium text-gray-900 mb-2">阅读偏好</h5>
              <p class="text-gray-700 text-sm">{{ formatArray(student?.additionalData.baseInfo.readingPreference) }}</p>
            </div>
            <div v-if="student?.additionalData?.baseInfo?.tokenForm?.length" class="bg-white p-3 rounded">
              <h5 class="font-medium text-gray-900 mb-2">代币形式</h5>
              <p class="text-gray-700 text-sm">{{ formatArray(student?.additionalData.baseInfo.tokenForm) }}</p>
            </div>
            <div v-if="student?.additionalData?.baseInfo?.foodPreference?.length" class="bg-white p-3 rounded">
              <h5 class="font-medium text-gray-900 mb-2">饮食偏好</h5>
              <p class="text-gray-700 text-sm">{{ formatArray(student?.additionalData.baseInfo.foodPreference) }}</p>
            </div>
            <div v-if="student?.additionalData?.baseInfo?.toyPreference?.length" class="bg-white p-3 rounded">
              <h5 class="font-medium text-gray-900 mb-2">物品或玩具偏好</h5>
              <p class="text-gray-700 text-sm">{{ formatArray(student?.additionalData.baseInfo.toyPreference) }}</p>
            </div>
          </div>

          <div v-if="student?.additionalData?.baseInfo?.activityPreference?.length" class="mt-4 bg-white p-3 rounded">
            <h5 class="font-medium text-gray-900 mb-2">活动或游戏偏好</h5>
            <p class="text-gray-700 text-sm">{{ formatArray(student?.additionalData.baseInfo.activityPreference) }}</p>
          </div>

          <div v-if="student?.additionalData?.baseInfo?.aversiveStimuli" class="mt-4 bg-white p-3 rounded">
            <h5 class="font-medium text-gray-900 mb-2">厌恶刺激</h5>
            <p class="text-gray-700 text-sm" v-html="marked(student?.additionalData.baseInfo.aversiveStimuli || '')" />
          </div>
        </div>
      </div>

      <!-- 发展历史信息板块 -->
      <div v-if="hasDetailedInfo" class="mb-4">
        <div
          class="flex items-center justify-between p-3 border rounded cursor-pointer hover:bg-gray-50"
          @click.stop="toggleSection('development')"
        >
          <div class="flex items-center gap-3">
            <div class="w-8 h-8 bg-purple-500 rounded flex items-center justify-center">
              <IconFile class="text-white text-sm" />
            </div>
            <span class="font-medium text-gray-900">发展历史信息</span>
          </div>
          <IconDown v-if="expandedSections.development" class="text-gray-400" />
          <IconRight v-else class="text-gray-400" />
        </div>

        <div v-if="expandedSections.development" class="mt-2 p-4 bg-gray-50 rounded">
          <div class="space-y-4">
            <div v-if="student?.additionalData?.baseInfo?.growthHistory" class="bg-white p-3 rounded">
              <h5 class="font-medium text-gray-900 mb-2">生长史</h5>
              <p
                class="text-gray-700 text-sm leading-relaxed"
                v-html="marked(student?.additionalData.baseInfo.growthHistory || '')"
              />
            </div>
            <div v-if="student?.additionalData?.baseInfo?.rehabilitationHistory" class="bg-white p-3 rounded">
              <h5 class="font-medium text-gray-900 mb-2">康复训练史与教育史</h5>
              <p
                class="text-gray-700 text-sm leading-relaxed"
                v-html="marked(student?.additionalData.baseInfo.rehabilitationHistory || '')"
              />
            </div>
            <div v-if="student?.additionalData?.baseInfo?.futureExpectations" class="bg-white p-3 rounded">
              <h5 class="font-medium text-gray-900 mb-2">未来发展期望</h5>
              <p
                class="text-gray-700 text-sm leading-relaxed"
                v-html="marked(student?.additionalData.baseInfo.futureExpectations || '')"
              />
            </div>
            <div v-if="student?.additionalData?.baseInfo?.recentGoals" class="bg-white p-3 rounded">
              <h5 class="font-medium text-gray-900 mb-2">近期发展目标期望</h5>
              <p
                class="text-gray-700 text-sm leading-relaxed"
                v-html="marked(student?.additionalData.baseInfo.recentGoals || '')"
              />
            </div>
            <!-- 兼容旧数据格式 -->
            <div v-if="student?.familyStatus" class="bg-white p-3 rounded">
              <h5 class="font-medium text-gray-900 mb-2">家庭状况</h5>
              <p class="text-gray-700 text-sm leading-relaxed">{{ student.familyStatus }}</p>
            </div>
            <div v-if="student?.historyOfDevelopment" class="bg-white p-3 rounded">
              <h5 class="font-medium text-gray-900 mb-2">发展史</h5>
              <p class="text-gray-700 text-sm leading-relaxed">{{ student.historyOfDevelopment }}</p>
            </div>
            <div v-if="student?.historyOfEducation" class="bg-white p-3 rounded">
              <h5 class="font-medium text-gray-900 mb-2">教育史</h5>
              <p class="text-gray-700 text-sm leading-relaxed">{{ student.historyOfEducation }}</p>
            </div>
          </div>
        </div>
      </div>

      <!-- 附件信息板块 -->
      <div v-if="hasAttachments" class="mb-4">
        <div
          class="flex items-center justify-between p-3 border rounded cursor-pointer hover:bg-gray-50"
          @click.stop="toggleSection('attachments')"
        >
          <div class="flex items-center gap-3">
            <div class="w-8 h-8 bg-purple-500 rounded flex items-center justify-center">
              <IconFile class="text-white text-sm" />
            </div>
            <span class="font-medium text-gray-900">家长反馈信息</span>
          </div>
          <IconDown v-if="expandedSections.attachments" class="text-gray-400" />
          <IconRight v-else class="text-gray-400" />
        </div>
        <div v-if="expandedSections.attachments" class="mt-2 p-4 bg-gray-50 rounded">
          <div class="space-y-3">
            <div
              v-for="(attachment, index) in student.attachments"
              :key="index"
              class="bg-white p-3 rounded cursor-pointer hover:bg-blue-100"
              @click="handleViewAttachment(attachment?.attachments)"
            >
              <div class="flex justify-between items-center">
                <span class="font-medium text-gray-900">{{ getTypeName(attachment.type) || '附件' }}</span>
                <span class="text-xs text-gray-500">{{ attachment.attachments?.length || 0 }} 个文件</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <attachment-preview-modal v-model="view" :current-file-index="0" :files-list="currentAttachment" />
  </a-modal>
</template>

<style scoped></style>
